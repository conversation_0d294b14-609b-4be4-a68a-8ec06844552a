import { NextRequest, NextResponse } from 'next/server';
import { 
  KMA_ENDPOINTS, 
  createKMAHeaders, 
  createCORSHeaders, 
  isValidKMAResponse,
  extractSignInToken,
  createFormData,
  createFetchOptions
} from '@/lib/config/kma-server';

/**
 * GET /api/kma/login
 * Fetches the KMA login page with ViewState tokens
 */
export async function GET(request: NextRequest) {
  try {
    // Create headers for KMA request
    const headers = createKMAHeaders();

    // Fetch login page from KMA server
    const response = await fetch(KMA_ENDPOINTS.LOGIN, {
      method: 'GET',
      headers,
      ...createFetchOptions(),
    });

    if (!isValidKMAResponse(response)) {
      console.error('KMA login page fetch failed:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch login page from KMA server' },
        { status: 502, headers: createCORSHeaders() }
      );
    }

    const html = await response.text();

    // Return HTML with CORS headers
    return new NextResponse(html, {
      status: 200,
      headers: {
        ...createCORSHeaders(),
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error fetching KMA login page:', error);
    return NextResponse.json(
      { error: 'Internal server error while fetching login page' },
      { status: 500, headers: createCORSHeaders() }
    );
  }
}

/**
 * POST /api/kma/login
 * Authenticates user with KMA server and returns SignIn token
 */
export async function POST(request: NextRequest) {
  try {
    // Get form data from request body
    const formData = await request.text();
    
    if (!formData) {
      return NextResponse.json(
        { error: 'Missing form data' },
        { status: 400, headers: createCORSHeaders() }
      );
    }

    // Create headers for KMA request
    const headers = createKMAHeaders(undefined, KMA_ENDPOINTS.LOGIN);

    // Send authentication request to KMA server
    const response = await fetch(KMA_ENDPOINTS.LOGIN, {
      method: 'POST',
      headers,
      body: formData,
      ...createFetchOptions(),
    });

    if (!isValidKMAResponse(response)) {
      console.error('KMA authentication failed:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401, headers: createCORSHeaders() }
      );
    }

    // Extract SignIn token from response
    const signInToken = await extractSignInToken(response);

    if (!signInToken) {
      // Authentication failed - no token returned
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401, headers: createCORSHeaders() }
      );
    }

    // Return the SignIn token
    return new NextResponse(signInToken, {
      status: 200,
      headers: {
        ...createCORSHeaders(),
        'Content-Type': 'text/plain',
      },
    });

  } catch (error) {
    console.error('Error during KMA authentication:', error);
    return NextResponse.json(
      { error: 'Internal server error during authentication' },
      { status: 500, headers: createCORSHeaders() }
    );
  }
}

/**
 * OPTIONS /api/kma/login
 * Handles CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: createCORSHeaders(),
  });
}
