import { NextRequest, NextResponse } from 'next/server';
import { 
  KMA_ENDPOINTS, 
  createKMAHeaders, 
  createCORSHeaders, 
  isValidKMAResponse,
  createFetchOptions
} from '@/lib/config/kma-server';

/**
 * GET /api/kma/schedule
 * Fetches current semester schedule from KMA server
 */
export async function GET(request: NextRequest) {
  try {
    // Get authentication token from headers
    const kmaCookie = request.headers.get('x-kma-cookie');
    
    if (!kmaCookie) {
      return NextResponse.json(
        { error: 'Missing authentication token' },
        { status: 401, headers: createCORSHeaders() }
      );
    }

    // Create headers for KMA request with authentication
    const headers = createKMAHeaders(kmaCookie, KMA_ENDPOINTS.SCHEDULE);

    // Fetch schedule data from KMA server
    const response = await fetch(KMA_ENDPOINTS.SCHEDULE, {
      method: 'GET',
      headers,
      ...createFetchOptions(),
    });

    if (!isValidKMAResponse(response)) {
      console.error('KMA schedule fetch failed:', response.status, response.statusText);
      
      // Check if it's an authentication error
      if (response.status === 401 || response.status === 403) {
        return NextResponse.json(
          { error: 'Authentication expired or invalid' },
          { status: 401, headers: createCORSHeaders() }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch schedule from KMA server' },
        { status: 502, headers: createCORSHeaders() }
      );
    }

    const html = await response.text();

    // Return HTML with CORS headers
    return new NextResponse(html, {
      status: 200,
      headers: {
        ...createCORSHeaders(),
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error fetching KMA schedule:', error);
    return NextResponse.json(
      { error: 'Internal server error while fetching schedule' },
      { status: 500, headers: createCORSHeaders() }
    );
  }
}

/**
 * POST /api/kma/schedule
 * Fetches specific semester schedule from KMA server
 */
export async function POST(request: NextRequest) {
  try {
    // Get authentication token from headers
    const kmaCookie = request.headers.get('x-kma-cookie');
    
    if (!kmaCookie) {
      return NextResponse.json(
        { error: 'Missing authentication token' },
        { status: 401, headers: createCORSHeaders() }
      );
    }

    // Get form data from request body
    const formData = await request.text();
    
    if (!formData) {
      return NextResponse.json(
        { error: 'Missing form data' },
        { status: 400, headers: createCORSHeaders() }
      );
    }

    // Create headers for KMA request with authentication
    const headers = createKMAHeaders(kmaCookie, KMA_ENDPOINTS.SCHEDULE);

    // Send POST request to KMA server for specific semester
    const response = await fetch(KMA_ENDPOINTS.SCHEDULE, {
      method: 'POST',
      headers,
      body: formData,
      ...createFetchOptions(),
    });

    if (!isValidKMAResponse(response)) {
      console.error('KMA schedule POST failed:', response.status, response.statusText);
      
      // Check if it's an authentication error
      if (response.status === 401 || response.status === 403) {
        return NextResponse.json(
          { error: 'Authentication expired or invalid' },
          { status: 401, headers: createCORSHeaders() }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch semester schedule from KMA server' },
        { status: 502, headers: createCORSHeaders() }
      );
    }

    const html = await response.text();

    // Return HTML with CORS headers
    return new NextResponse(html, {
      status: 200,
      headers: {
        ...createCORSHeaders(),
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error fetching KMA semester schedule:', error);
    return NextResponse.json(
      { error: 'Internal server error while fetching semester schedule' },
      { status: 500, headers: createCORSHeaders() }
    );
  }
}

/**
 * OPTIONS /api/kma/schedule
 * Handles CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: createCORSHeaders(),
  });
}
