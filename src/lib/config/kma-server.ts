/**
 * KMA Server Configuration
 * 
 * This file contains configuration constants and utilities for communicating
 * with the KMA (Academy of Military Science and Technology) server.
 */

// KMA Server Base Configuration
export const KMA_CONFIG = {
  BASE_URL: process.env.KMA_BASE_URL || 'http://qldt.actvn.edu.vn',
  LOGIN_PATH: process.env.KMA_LOGIN_PATH || '/CMCSoft.IU.Web.info/Login.aspx',
  SCHEDULE_PATH: process.env.KMA_SCHEDULE_PATH || '/CMCSoft.IU.Web.Info/Reports/Form/StudentTimeTable.aspx',
} as const;

// Full URLs for KMA endpoints
export const KMA_ENDPOINTS = {
  LOGIN: `${KMA_CONFIG.BASE_URL}${KMA_CONFIG.LOGIN_PATH}`,
  SCHEDULE: `${KMA_CONFIG.BASE_URL}${KMA_CONFIG.SCHEDULE_PATH}`,
} as const;

// Request headers for KMA server communication
export const KMA_HEADERS = {
  'Content-Type': 'application/x-www-form-urlencoded',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
  'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
  'Accept-Encoding': 'gzip, deflate',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1',
} as const;

// CORS headers for API responses
export const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, x-kma-cookie',
  'Access-Control-Max-Age': '86400',
} as const;

/**
 * Creates headers for KMA server requests
 * @param cookie - Optional cookie string for authenticated requests
 * @param origin - Optional origin URL to set referer headers
 * @returns Headers object for fetch requests
 */
export function createKMAHeaders(cookie?: string, origin?: string): HeadersInit {
  const headers: HeadersInit = {
    ...KMA_HEADERS,
  };

  if (cookie) {
    headers['Cookie'] = cookie;
  }

  if (origin) {
    headers['Origin'] = origin;
    headers['Referer'] = origin;
  } else {
    headers['Origin'] = KMA_CONFIG.BASE_URL;
    headers['Referer'] = KMA_CONFIG.BASE_URL;
  }

  return headers;
}

/**
 * Creates CORS response headers
 * @returns Headers object with CORS settings
 */
export function createCORSHeaders(): HeadersInit {
  return { ...CORS_HEADERS };
}

/**
 * Validates KMA server response
 * @param response - Fetch response object
 * @returns True if response is valid
 */
export function isValidKMAResponse(response: Response): boolean {
  return response.ok && response.status >= 200 && response.status < 300;
}

/**
 * Extracts SignIn token from KMA response
 * @param response - Response from KMA login endpoint
 * @returns SignIn token string or null if not found
 */
export async function extractSignInToken(response: Response): Promise<string | null> {
  // Check Set-Cookie header first
  const setCookieHeader = response.headers.get('Set-Cookie');
  if (setCookieHeader) {
    const tokenMatch = setCookieHeader.match(/SignIn=([^;]+)/);
    if (tokenMatch && tokenMatch[1]) {
      return `SignIn=${tokenMatch[1]}`;
    }
  }

  // If no cookie in headers, check response body
  const responseText = await response.text();
  if (responseText && responseText.startsWith('SignIn=')) {
    return responseText;
  }

  return null;
}

/**
 * Creates form data string from object
 * @param data - Object with form field values
 * @returns URL-encoded form data string
 */
export function createFormData(data: Record<string, string>): string {
  return Object.keys(data)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key] || '')}`)
    .join('&');
}

/**
 * Request timeout configuration
 */
export const REQUEST_TIMEOUT = 30000; // 30 seconds

/**
 * Creates fetch options with timeout
 * @param options - Standard fetch options
 * @returns Fetch options with timeout
 */
export function createFetchOptions(options: RequestInit = {}): RequestInit {
  return {
    ...options,
    signal: AbortSignal.timeout(REQUEST_TIMEOUT),
  };
}
