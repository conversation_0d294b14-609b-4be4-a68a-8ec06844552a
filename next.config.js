/** @type {import('next').NextConfig} */
const nextConfig = {
	images: {
		domains: []
	},
	async headers() {
		return [
			{
				// Apply CORS headers to API routes
				source: '/api/:path*',
				headers: [
					{
						key: 'Access-Control-Allow-Origin',
						value: '*'
					},
					{
						key: 'Access-Control-Allow-Methods',
						value: 'GET, POST, PUT, DELETE, OPTIONS'
					},
					{
						key: 'Access-Control-Allow-Headers',
						value: 'Content-Type, Authorization, x-kma-cookie'
					},
					{
						key: 'Access-Control-Max-Age',
						value: '86400'
					}
				]
			}
		];
	}
};

module.exports = nextConfig;
