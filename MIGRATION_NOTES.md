# Migration from CORS Proxy to Direct Integration

## Overview

This document outlines the migration from using an external Cloudflare Workers CORS proxy to direct integration with the KMA server through Next.js API routes.

## Changes Made

### 1. Removed External Dependencies

**Before:**
- External CORS proxy: `https://actvn-schedule.cors-ngosangns.workers.dev`
- Custom header format: `x-cors-headers: {"Cookie": "SignIn=..."}`

**After:**
- Next.js API routes: `/api/kma/login`, `/api/kma/schedule`
- Standard header format: `x-kma-cookie: SignIn=...`

### 2. New API Routes

#### `/api/kma/login` (GET/POST)
- Handles authentication with KMA server
- Extracts ViewState tokens for login
- Returns SignIn session token

#### `/api/kma/schedule` (GET/POST)
- Fetches schedule data from KMA server
- Handles semester switching
- Requires authentication token

### 3. Updated Configuration

#### New Files:
- `src/lib/config/kma-server.ts` - KMA server configuration
- `src/app/api/kma/login/route.ts` - Login API route
- `src/app/api/kma/schedule/route.ts` - Schedule API route

#### Modified Files:
- `src/lib/ts/calendar.ts` - Updated API calls
- `src/lib/ts/user.ts` - Updated authentication
- `next.config.js` - Added CORS headers
- `.env.example` - Updated environment variables

### 4. Environment Variables

New optional environment variables for configuration:
```bash
# KMA Server Configuration (Optional - defaults are set in code)
KMA_BASE_URL=http://qldt.actvn.edu.vn
KMA_LOGIN_PATH=/CMCSoft.IU.Web.info/Login.aspx
KMA_SCHEDULE_PATH=/CMCSoft.IU.Web.Info/Reports/Form/StudentTimeTable.aspx
```

## Benefits

### 1. Reduced Dependencies
- No reliance on external proxy services
- Self-contained application
- Better control over request handling

### 2. Improved Security
- Server-side handling of sensitive requests
- No exposure of KMA credentials to external services
- Better error handling and validation

### 3. Enhanced Performance
- Fewer network hops (Client → API Route → KMA vs Client → Proxy → KMA)
- Better caching control
- Reduced latency

### 4. Better Reliability
- No single point of failure from external proxy
- Direct control over error handling
- Easier debugging and monitoring

### 5. Easier Maintenance
- All code within the application codebase
- No need to maintain separate proxy service
- Unified deployment and versioning

## Technical Details

### Request Flow

**Before (with CORS Proxy):**
```
Client → Cloudflare Workers Proxy → KMA Server → Proxy → Client
```

**After (Direct Integration):**
```
Client → Next.js API Route → KMA Server → API Route → Client
```

### Header Changes

**Before:**
```javascript
headers: {
  'x-cors-headers': JSON.stringify({
    Cookie: signInToken
  })
}
```

**After:**
```javascript
headers: {
  'x-kma-cookie': signInToken
}
```

### Error Handling

The new implementation provides better error handling:
- HTTP status codes for different error types
- Detailed error messages
- Proper validation of responses

## Migration Steps

If you're updating an existing installation:

1. **Pull the latest code**
2. **Install dependencies** (no new dependencies required)
3. **Update environment variables** (optional)
4. **Test the application** with your KMA credentials

## Testing

The integration tests continue to work with the new implementation:
```bash
# Run integration tests
npm run test:integration
```

Make sure your `.env` file has valid test credentials:
```bash
TEST_USERNAME=your_kma_username
TEST_PASSWORD=your_kma_password
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: The Next.js API routes handle CORS automatically
2. **Authentication Failures**: Check that credentials are valid
3. **Network Timeouts**: The direct integration should be faster

### Debug Mode

Enable debug logging by setting:
```bash
LOG_LEVEL=debug
```

## Rollback Plan

If needed, the previous proxy-based implementation can be restored by:
1. Reverting the changes in `calendar.ts` and `user.ts`
2. Removing the new API routes
3. Using the original proxy URLs

However, the new direct integration is recommended for all the benefits listed above.

## Support

For issues or questions about this migration:
1. Check the updated documentation in `/docs`
2. Run the integration tests to verify functionality
3. Review the API route implementations for debugging

The migration maintains full backward compatibility with existing data and user sessions.
